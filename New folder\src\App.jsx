import React, { useEffect, useState, Suspense } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import { motion, AnimatePresence } from 'framer-motion'
import { QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

// React Query Configuration
import { queryClient } from './config/reactQuery'

// Context Providers
import { AuthProvider, useAuth } from './context/AuthContext'
import { ThemeProvider } from './context/ThemeContext'
import { SiteProvider } from './context/SiteContext'

// Components
import ProtectedRoute from './components/auth/ProtectedRoute'
import LoadingSpinner from './components/common/LoadingSpinner'
import BannedAccountMessage from './components/common/BannedAccountMessage'

// Public Pages (not lazy loaded for better initial performance)
import Home from './pages/Home'
import Login from './pages/Login'
import Register from './pages/Register'
import NotFound from './pages/NotFound'
import ReactQueryTest from './pages/ReactQueryTest'

// Lazy loaded components for code splitting
import * as LazyComponents from './components/lazy/LazyComponents'



// Services
import { setupService } from './services/api'

function App() {
  const [isSetupComplete, setIsSetupComplete] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkSetupStatus()
  }, [])

  const checkSetupStatus = async () => {
    try {
      const response = await setupService.checkSetup()
      setIsSetupComplete(response.isComplete)
    } catch (error) {
      // If setup endpoint doesn't exist, assume setup is needed
      setIsSetupComplete(false)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner />
      </div>
    )
  }

  // Show setup wizard if setup is not complete
  if (isSetupComplete === false) {
    return (
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <Suspense fallback={<LazyComponents.LazyLoadingSpinner />}>
              <LazyComponents.SetupWizard onComplete={() => setIsSetupComplete(true)} />
            </Suspense>
            <Toaster position="top-right" />
          </div>
        </ThemeProvider>
      </QueryClientProvider>
    )
  }

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <SiteProvider>
          <AuthProvider>
            <AppContent />
          </AuthProvider>
        </SiteProvider>
      </ThemeProvider>
      {/* React Query DevTools - only in development */}
      {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  )
}

const AppContent = () => {
  const { banInfo, logout } = useAuth()

  return (
    <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Show banned account message as overlay if user is banned */}
        {banInfo && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 z-50 bg-gray-50 dark:bg-gray-900"
          >
            <BannedAccountMessage
              banReason={banInfo.reason}
              banDate={banInfo.date}
              onLogout={logout}
            />
          </motion.div>
        )}

        <AnimatePresence mode="wait">
          <Suspense fallback={<LazyComponents.LazyLoadingSpinner />}>
            <Routes>
                {/* Public Routes */}
                <Route path="/" element={<Home />} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/react-query-test" element={<ReactQueryTest />} />
                <Route path="/categories/:slug" element={<LazyComponents.CategoryView />} />
                <Route path="/levels/:slug" element={<LazyComponents.LevelView />} />
                <Route path="/classes/:slug" element={<LazyComponents.ClassView />} />
                <Route path="/redeem/:code" element={<LazyComponents.RedeemCode />} />

                {/* Admin Routes */}
                <Route path="/admin" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.AdminDashboard />
                  </ProtectedRoute>
                } />
                <Route path="/admin/courses" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.CourseManagement />
                  </ProtectedRoute>
                } />
                <Route path="/admin/courses/create" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.CreateCourse />
                  </ProtectedRoute>
                } />
                <Route path="/admin/courses/:id/edit" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.EditCourse />
                  </ProtectedRoute>
                } />
                <Route path="/admin/panel" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.AdminPanel />
                  </ProtectedRoute>
                } />
                <Route path="/admin/users" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.UserManagement />
                  </ProtectedRoute>
                } />
                <Route path="/admin/users/create" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.CreateUser />
                  </ProtectedRoute>
                } />
                <Route path="/admin/users/:id/edit" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.EditUser />
                  </ProtectedRoute>
                } />
                <Route path="/admin/watermark-settings" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.WatermarkSettings />
                  </ProtectedRoute>
                } />
                <Route path="/admin/homepage-settings" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.HomepageSettings />
                  </ProtectedRoute>
                } />
                <Route path="/admin/notification-settings" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.NotificationSettings />
                  </ProtectedRoute>
                } />
                <Route path="/admin/site-settings" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.SiteSettings />
                  </ProtectedRoute>
                } />
                <Route path="/admin/general-settings" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.GeneralSettings />
                  </ProtectedRoute>
                } />
                <Route path="/admin/banned-accounts" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.BannedAccounts />
                  </ProtectedRoute>
                } />
                <Route path="/admin/categories" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.CategoryManagement />
                  </ProtectedRoute>
                } />
                <Route path="/admin/levels" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.LevelManagement />
                  </ProtectedRoute>
                } />
                <Route path="/admin/languages" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.LanguageManagement />
                  </ProtectedRoute>
                } />
                <Route path="/admin/classes" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.ClassManagement />
                  </ProtectedRoute>
                } />
                <Route path="/admin/payment-codes" element={
                  <ProtectedRoute requiredRole="admin">
                    <LazyComponents.PaymentCodes />
                  </ProtectedRoute>
                } />

                {/* Instructor Routes */}
                <Route path="/instructor" element={
                  <ProtectedRoute requiredRole={["admin", "instructor"]}>
                    <LazyComponents.InstructorDashboard />
                  </ProtectedRoute>
                } />
                <Route path="/instructor/courses" element={
                  <ProtectedRoute requiredRole={["admin", "instructor"]}>
                    <LazyComponents.MyCourses />
                  </ProtectedRoute>
                } />
                <Route path="/instructor/courses/:courseId/sections/create" element={
                  <ProtectedRoute requiredRole={["admin", "instructor"]}>
                    <LazyComponents.CreateSection />
                  </ProtectedRoute>
                } />
                <Route path="/instructor/sections/:sectionId/lessons/create" element={
                  <ProtectedRoute requiredRole={["admin", "instructor"]}>
                    <LazyComponents.CreateLesson />
                  </ProtectedRoute>
                } />
                <Route path="/instructor/courses/:courseId/quizzes/create" element={
                  <ProtectedRoute requiredRole={["admin", "instructor"]}>
                    <LazyComponents.CreateQuiz />
                  </ProtectedRoute>
                } />
                <Route path="/instructor/results" element={
                  <ProtectedRoute requiredRole={["admin", "instructor"]}>
                    <LazyComponents.StudentResults />
                  </ProtectedRoute>
                } />

                {/* Student Routes */}
                <Route path="/dashboard" element={
                  <ProtectedRoute>
                    <LazyComponents.StudentDashboard />
                  </ProtectedRoute>
                } />
                <Route path="/courses" element={
                  <ProtectedRoute>
                    <LazyComponents.Courses />
                  </ProtectedRoute>
                } />
                <Route path="/courses/:id" element={
                  <ProtectedRoute>
                    <LazyComponents.CourseView />
                  </ProtectedRoute>
                } />
                <Route path="/lessons/:id" element={
                  <ProtectedRoute>
                    <LazyComponents.LessonView />
                  </ProtectedRoute>
                } />
                <Route path="/quizzes/:id" element={
                  <ProtectedRoute>
                    <LazyComponents.QuizView />
                  </ProtectedRoute>
                } />
                <Route path="/profile" element={
                  <ProtectedRoute>
                    <LazyComponents.Profile />
                  </ProtectedRoute>
                } />


                {/* 404 Route */}
                <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
        </AnimatePresence>
        <Toaster position="top-right" />
      </div>
    </Router>
  )
}

export default App
