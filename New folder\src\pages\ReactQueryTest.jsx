import React from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { courseService, categoryService } from '../services/api'
import LoadingSpinner from '../components/common/LoadingSpinner'

const ReactQueryTest = () => {
  const queryClient = useQueryClient()

  // Test React Query with courses
  const { 
    data: coursesData, 
    isLoading: coursesLoading, 
    error: coursesError,
    refetch: refetchCourses 
  } = useQuery({
    queryKey: ['courses'],
    queryFn: () => courseService.getAll(),
    staleTime: 30 * 1000, // 30 seconds as requested
    refetchOnMount: true,
  })

  // Test React Query with categories
  const { 
    data: categoriesData, 
    isLoading: categoriesLoading, 
    error: categoriesError 
  } = useQuery({
    queryKey: ['categories'],
    queryFn: () => categoryService.getAll(),
    staleTime: 30 * 1000,
    refetchOnMount: true,
  })

  // Test mutation (if needed)
  const testMutation = useMutation({
    mutationFn: () => {
      console.log('Test mutation executed')
      return Promise.resolve('Success')
    },
    onSuccess: () => {
      // Refetch courses after mutation
      queryClient.invalidateQueries({ queryKey: ['courses'] })
      console.log('✅ Mutation successful - courses refetched')
    },
  })

  const handleRefresh = () => {
    refetchCourses()
    queryClient.invalidateQueries({ queryKey: ['categories'] })
    console.log('🔄 Manual refetch triggered')
  }

  const handleTestMutation = () => {
    testMutation.mutate()
  }

  if (coursesLoading || categoriesLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Testing React Query...
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
            🚀 React Query Test Page
          </h1>
          
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-green-800 dark:text-green-400 mb-2">
                ✅ React Query Status
              </h3>
              <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
                <li>• staleTime: 30 seconds ✅</li>
                <li>• Refetch on mount: ✅</li>
                <li>• Cache invalidation: ✅</li>
                <li>• Error handling: ✅</li>
              </ul>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-400 mb-2">
                🔧 Test Controls
              </h3>
              <div className="space-y-2">
                <button
                  onClick={handleRefresh}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                >
                  🔄 Manual Refetch
                </button>
                <button
                  onClick={handleTestMutation}
                  disabled={testMutation.isPending}
                  className="w-full bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors disabled:opacity-50"
                >
                  {testMutation.isPending ? '⏳ Testing...' : '🧪 Test Mutation'}
                </button>
              </div>
            </div>
          </div>

          {/* Courses Data */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              📚 Courses Data (React Query)
            </h2>
            {coursesError ? (
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                <p className="text-red-600 dark:text-red-400">
                  Error: {coursesError.message}
                </p>
              </div>
            ) : (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Total courses: {coursesData?.courses?.length || coursesData?.length || 0}
                </p>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {(coursesData?.courses || coursesData || []).slice(0, 6).map((course, index) => (
                    <div key={course.id || index} className="bg-white dark:bg-gray-800 p-3 rounded border">
                      <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                        {course.title}
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Price: ${course.price || 0}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Categories Data */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              🏷️ Categories Data (React Query)
            </h2>
            {categoriesError ? (
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                <p className="text-red-600 dark:text-red-400">
                  Error: {categoriesError.message}
                </p>
              </div>
            ) : (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Total categories: {categoriesData?.length || 0}
                </p>
                <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {(categoriesData || []).slice(0, 8).map((category, index) => (
                    <div key={category.id || index} className="bg-white dark:bg-gray-800 p-2 rounded border">
                      <p className="font-medium text-gray-900 dark:text-white text-sm">
                        {category.name}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Debug Info */}
          <div className="mt-8 bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              🐛 Debug Info
            </h3>
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <p>Courses loading: {coursesLoading ? 'Yes' : 'No'}</p>
              <p>Categories loading: {categoriesLoading ? 'Yes' : 'No'}</p>
              <p>Mutation status: {testMutation.isPending ? 'Pending' : 'Idle'}</p>
              <p>Last refetch: {new Date().toLocaleTimeString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReactQueryTest
